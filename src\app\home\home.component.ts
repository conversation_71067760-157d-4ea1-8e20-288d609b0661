import { Component } from '@angular/core';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [MatToolbarModule, MatButtonModule, MatCardModule],
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.css']
})
export class HomeComponent {
  companies = [
    { name: 'Entreprise A', logo: 'https://via.placeholder.com/200', description: 'Leader en technologie.' },
    { name: 'Entreprise B', logo: 'https://via.placeholder.com/200', description: 'Spécialiste en mode.' },
    { name: 'Entreprise C', logo: 'https://via.placeholder.com/200', description: 'Innovateur en alimentation.' }
  ];

  influencerCount = 1500; // Simulé, à remplacer par une requête API
}
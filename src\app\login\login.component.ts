import { Component } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { FormsModule } from '@angular/forms';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatSelectModule,
    FormsModule,
    RouterLink
  ],
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginComponent {
  usernameOrEmail: string = '';
  password: string = '';
  clientType: string = '';
  usernameOrEmailError: boolean = false;
  passwordError: boolean = false;
  clientTypeError: boolean = false;

  onSubmit() {
    // Validation simple
    this.usernameOrEmailError = !this.usernameOrEmail;
    this.passwordError = !this.password;
    this.clientTypeError = !this.clientType;

    if (!this.usernameOrEmailError && !this.passwordError && !this.clientTypeError) {
      console.log('Connexion avec:', {
        usernameOrEmail: this.usernameOrEmail,
        password: this.password,
        clientType: this.clientType
      });
      // TODO: Appeler l'API Laravel pour la connexion
    }
  }
}
<div class="login-container">
  <mat-card class="login-card">
    <mat-card-header>
      <mat-card-title>Connexion à Buzzmark</mat-card-title>
      <mat-card-subtitle>Accédez à votre compte pour commencer</mat-card-subtitle>
    </mat-card-header>
    <mat-card-content>
      <form class="login-form" (ngSubmit)="onSubmit()">
        <!-- Champ Email ou Nom d'utilisateur -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Email ou Nom d'utilisateur</mat-label>
          <input matInput [(ngModel)]="usernameOrEmail" name="usernameOrEmail" required>
          <mat-error *ngIf="usernameOrEmailError">Ce champ est requis</mat-error>
        </mat-form-field>

        <!-- Champ Mot de passe -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Mot de passe</mat-label>
          <input matInput [(ngModel)]="password" name="password" type="password" required>
          <mat-error *ngIf="passwordError">Ce champ est requis</mat-error>
        </mat-form-field>

        <!-- Type de client -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Type de client</mat-label>
          <mat-select [(ngModel)]="clientType" name="clientType" required>
            <mat-option value="entreprise">Entreprise</mat-option>
            <mat-option value="influenceur">Influenceur</mat-option>
          </mat-select>
          <mat-error *ngIf="clientTypeError">Veuillez sélectionner un type</mat-error>
        </mat-form-field>

        <!-- Bouton Connexion -->
        <button mat-raised-button color="primary" type="submit" class="full-width login-btn">Se connecter</button>
      </form>

      <!-- Connexion Google (Placeholder) -->
      <div class="google-login" *ngIf="clientType === 'entreprise'">
        <p>Ou connectez-vous avec Google (bientôt disponible)</p>
        <button mat-stroked-button disabled>Connexion avec Google</button>
      </div>

      <!-- Lien vers inscription -->
      <p class="register-link">
        Pas de compte ? <a [routerLink]="['/register']">Inscrivez-vous</a>
      </p>
    </mat-card-content>
  </mat-card>
</div>
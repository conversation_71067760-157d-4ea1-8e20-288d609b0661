.navbar {
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.7), transparent);
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1000;
  padding: 10px 20px;
  display: flex;
  align-items: center;
}

.logo {
  font-family: 'Montserrat', sans-serif;
  font-weight: bold;
  font-size: 24px;
  color: #f97316; /* Orange vif */
}

.spacer {
  flex: 1 1 auto;
}

.nav-links {
  display: flex;
  gap: 20px;
}

.nav-link {
  color: white;
  font-size: 16px;
  position: relative;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #f97316; /* Orange sur hover */
}

.nav-link:hover::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  background: #f97316; /* Ligne orange */
  bottom: -4px;
  left: 0;
  animation: slideIn 0.3s ease-in-out;
}

.nav-link.active {
  color: #f97316; /* Home orange par défaut */
}

.nav-link.active::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  background: #f97316; /* Ligne orange pour Home */
  bottom: -4px;
  left: 0;
}

.login-btn {
  background: rgb(11, 11, 11);
  color: white;
  border: 2px solid #f97316; /* Bordure orange */
  border-radius: 4px;
  padding: 8px 16px;
  transition: color 0.3s ease;
}

.login-btn:hover {
  color: #f97316; /* Texte orange sur hover */
}

.hero {
  height: 100vh;
  background: linear-gradient(135deg, #1e3a8a, #f97316);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: white;
  position: relative;
  overflow: hidden;
}

.hero-title {
  font-size: 48px;
  font-family: 'Montserrat', sans-serif;
  font-weight: bold;
  animation: fadeInDown 1s ease-in-out;
}

.hero-subtitle {
  font-size: 24px;
  margin: 20px 0;
  animation: fadeInUp 1s ease-in-out;
}

.hero-buttons {
  display: flex;
  gap: 20px;
}

.hero-btn {
  padding: 10px 20px;
  font-size: 18px;
  transition: transform 0.3s ease;
}

.hero-btn:hover {
  transform: scale(1.05);
}

.moving-images {
  position: absolute;
  bottom: 20px;
  display: flex;
  gap: 20px;
  animation: slide 20s linear infinite;
}

.moving-img {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.companies-section, .influencers-section {
  padding: 50px 20px;
  text-align: center;
  background: #f4f4f4;
}

.companies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap能力: 20px;
  margin-top: 20px;
}

.company-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.company-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.influencer-stats {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
}

.stat-card {
  background: #1e3a8a;
  color: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  animation: pulse 2s infinite;
}

.footer {
  background: #1e3a8a;
  color: white;
  padding: 40px 20px;
  text-align: center;
}

.footer-content {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: 20px;
}

.footer-section {
  min-width: 200px;
}

.social-link {
  display: block;
  color: #f97316;
  margin: 5px 0;
  text-decoration: none;
}

.footer-bottom {
  margin-top: 20px;
  font-size: 14px;
}

@keyframes slide {
  0% { transform: translateX(100vw); }
  100% { transform: translateX(-100%); }
}

@keyframes fadeInDown {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes slideIn {
  from { width: 0; }
  to { width: 100%; }
}